<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Menu extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'route',
        'icon',
        'parent_id',
        'sort_order',
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Get the parent menu.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Menu::class, 'parent_id');
    }

    /**
     * Get the child menus.
     */
    public function children(): Has<PERSON>any
    {
        return $this->hasMany(Menu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get all descendants recursively.
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Scope to get root menus (no parent).
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id')->orderBy('sort_order');
    }

    /**
     * Scope to search menus by name.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('route', 'like', '%' . $search . '%');
    }

    /**
     * Get the next sort order for a given parent.
     */
    public static function getNextSortOrder($parentId = null): int
    {
        return static::where('parent_id', $parentId)->max('sort_order') + 1;
    }

    /**
     * Update sort orders for menus.
     */
    public static function updateSortOrders(array $menuIds): void
    {
        foreach ($menuIds as $index => $menuId) {
            static::where('id', $menuId)->update(['sort_order' => $index + 1]);
        }
    }
}
