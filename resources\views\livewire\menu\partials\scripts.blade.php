<!-- Drag & Drop Styles -->
<style>
    .sortable-ghost {
        opacity: 0.5;
        background: rgba(59, 130, 246, 0.1);
        border: 2px dashed rgba(59, 130, 246, 0.3);
    }

    .sortable-chosen {
        transform: rotate(2deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .sortable-drag {
        transform: rotate(5deg) scale(1.02);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }

    .drag-handle:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .dark .drag-handle:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    /* Accordion animations */
    .submenu-container {
        transition: all 0.3s ease-in-out;
    }

    .submenu-container.hidden {
        max-height: 0;
        opacity: 0;
        margin-top: 0;
        margin-bottom: 0;
    }

    .submenu-container:not(.hidden) {
        max-height: 1000px;
        opacity: 1;
    }

    /* Accordion toggle button */
    .accordion-toggle {
        transition: all 0.2s ease;
    }

    .accordion-toggle:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .dark .accordion-toggle:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    /* Chevron animation */
    .accordion-toggle svg {
        transition: transform 0.2s ease;
    }
</style>

<!-- Drag & Drop JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
    // Accordion functionality
    function toggleAccordion(menuId) {
        const submenu = document.getElementById('submenu-' + menuId);
        const chevron = document.getElementById('chevron-' + menuId.replace('menu-', ''));

        if (submenu && chevron) {
            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(90deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }
    }

    // Make toggleAccordion globally available
    window.toggleAccordion = toggleAccordion;

    function initializeDragAndDrop() {
        const menuList = document.getElementById('menu-list');

        if (!menuList) {
            return;
        }

        if (typeof Sortable === 'undefined') {
            return;
        }

        // Destroy existing sortable instances
        if (menuList.sortableInstance) {
            menuList.sortableInstance.destroy();
        }

        // Initialize main menu sortable
        menuList.sortableInstance = new Sortable(menuList, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                // Get ordered IDs for main menus only
                const orderedIds = [];
                const menuItems = menuList.querySelectorAll('.menu-item[data-menu-id]');
                menuItems.forEach(function(item) {
                    const menuId = item.getAttribute('data-menu-id');
                    if (menuId) {
                        orderedIds.push(menuId);
                    }
                });

                // Call Livewire method
                if (window.Livewire && orderedIds.length > 0) {
                    const componentId = document.querySelector('[wire\\:id]').getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component) {
                        component.call('reorderMenus', orderedIds);
                    }
                }
            }
        });

        // Initialize submenu sortables
        const submenuLists = document.querySelectorAll('.submenu-list');
        submenuLists.forEach(function(submenuList) {
            if (submenuList.sortableInstance) {
                submenuList.sortableInstance.destroy();
            }

            submenuList.sortableInstance = new Sortable(submenuList, {
                handle: '.submenu-drag-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function(evt) {
                    // Get ordered IDs for this submenu
                    const orderedIds = [];
                    const submenuItems = submenuList.querySelectorAll('.submenu-item[data-menu-id]');
                    submenuItems.forEach(function(item) {
                        const menuId = item.getAttribute('data-menu-id');
                        if (menuId) {
                            orderedIds.push(menuId);
                        }
                    });

                    // Call Livewire method
                    if (window.Livewire && orderedIds.length > 0) {
                        const componentId = document.querySelector('[wire\\:id]').getAttribute('wire:id');
                        const component = window.Livewire.find(componentId);
                        if (component) {
                            component.call('reorderMenus', orderedIds);
                        }
                    }
                }
            });
        });
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeDragAndDrop);

    // Reinitialize after Livewire updates
    document.addEventListener('livewire:load', function() {
        initializeDragAndDrop();
    });

    document.addEventListener('livewire:update', function() {
        setTimeout(initializeDragAndDrop, 100);
    });
</script>