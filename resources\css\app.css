@import "tailwindcss";
@import "../../vendor/livewire/flux/dist/flux.css";

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --color-zinc-50: var(--color-stone-50);
    --color-zinc-100: var(--color-stone-100);
    --color-zinc-200: var(--color-stone-200);
    --color-zinc-300: var(--color-stone-300);
    --color-zinc-400: var(--color-stone-400);
    --color-zinc-500: var(--color-stone-500);
    --color-zinc-600: var(--color-stone-600);
    --color-zinc-700: var(--color-stone-700);
    --color-zinc-800: var(--color-stone-800);
    --color-zinc-900: var(--color-stone-900);
    --color-zinc-950: var(--color-stone-950);
}

@theme {
    --color-accent: var(--color-lime-400);
    --color-accent-content: var(--color-lime-600);
    --color-accent-foreground: var(--color-lime-900);
}

@layer theme {
    .dark {
        --color-accent: var(--color-lime-400);
        --color-accent-content: var(--color-lime-400);
        --color-accent-foreground: var(--color-lime-950);
    }
}

@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field]:not(ui-radio, ui-checkbox) {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */
