<!-- Toast Notifications -->
<div
    x-data="{
        show: false,
        message: '',
        type: 'success',
        showToast(msg, toastType = 'success') {
            this.message = msg;
            this.type = toastType;
            this.show = true;
            setTimeout(() => { this.show = false; }, 4000);
        }
    }"
    x-init="
        @if (session()->has('message'))
            showToast('{{ session('message') }}', 'success');
        @endif
        @if (session()->has('error'))
            showToast('{{ session('error') }}', 'error');
        @endif
    "
    @menu-created.window="showToast('Menu created successfully!', 'success')"
    @menu-updated.window="showToast('Menu updated successfully!', 'success')"
    @menu-deleted.window="showToast('Menu deleted successfully!', 'success')"
    @menu-reordered.window="showToast('Menu order updated successfully!', 'success')"
>
    <!-- Toast Container -->
    <div
        x-show="show"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform translate-y-2"
        class="fixed top-4 right-4 z-50 max-w-sm w-full"
        style="display: none;"
    >
        <div
            class="p-4 rounded-lg shadow-lg border"
            :class="{
                'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800': type === 'success',
                'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800': type === 'error',
                'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800': type === 'info'
            }"
        >
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <template x-if="type === 'success'">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </template>
                    <template x-if="type === 'error'">
                        <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </template>
                </div>
                <div class="ml-3">
                    <p
                        class="text-sm font-medium"
                        :class="{
                            'text-green-800 dark:text-green-200': type === 'success',
                            'text-red-800 dark:text-red-200': type === 'error',
                            'text-blue-800 dark:text-blue-200': type === 'info'
                        }"
                        x-text="message"
                    ></p>
                </div>
                <div class="ml-auto pl-3">
                    <button
                        @click="show = false"
                        class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
                        :class="{
                            'text-green-500 hover:bg-green-100 focus:ring-green-600 dark:text-green-400 dark:hover:bg-green-900/50': type === 'success',
                            'text-red-500 hover:bg-red-100 focus:ring-red-600 dark:text-red-400 dark:hover:bg-red-900/50': type === 'error',
                            'text-blue-500 hover:bg-blue-100 focus:ring-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/50': type === 'info'
                        }"
                    >
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>