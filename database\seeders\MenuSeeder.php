<?php

namespace Database\Seeders;

use App\Models\Menu;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create root menus
        $dashboard = Menu::create([
            'name' => 'Dashboard',
            'route' => 'dashboard',
            'icon' => 'layout-grid',
            'sort_order' => 1,
        ]);

        $users = Menu::create([
            'name' => 'Users',
            'route' => 'users.index',
            'icon' => null, // Using null for now since users icon doesn't exist
            'sort_order' => 2,
        ]);

        $documents = Menu::create([
            'name' => 'Documents',
            'route' => 'documents.index',
            'icon' => 'folder-git-2',
            'sort_order' => 3,
        ]);

        $settings = Menu::create([
            'name' => 'Settings',
            'route' => 'settings.profile',
            'icon' => null, // Using null for now since cog icon doesn't exist
            'sort_order' => 4,
        ]);

        // Create sub-menus for Users
        Menu::create([
            'name' => 'All Users',
            'route' => 'users.index',
            'icon' => null,
            'parent_id' => $users->id,
            'sort_order' => 1,
        ]);

        Menu::create([
            'name' => 'Add User',
            'route' => 'users.create',
            'icon' => null,
            'parent_id' => $users->id,
            'sort_order' => 2,
        ]);

        // Create sub-menus for Documents
        Menu::create([
            'name' => 'All Documents',
            'route' => 'documents.index',
            'icon' => null,
            'parent_id' => $documents->id,
            'sort_order' => 1,
        ]);

        Menu::create([
            'name' => 'Upload Document',
            'route' => 'documents.create',
            'icon' => null,
            'parent_id' => $documents->id,
            'sort_order' => 2,
        ]);

        Menu::create([
            'name' => 'Categories',
            'route' => 'documents.categories',
            'icon' => null,
            'parent_id' => $documents->id,
            'sort_order' => 3,
        ]);

        // Create sub-menus for Settings
        Menu::create([
            'name' => 'Profile',
            'route' => 'settings.profile',
            'icon' => null,
            'parent_id' => $settings->id,
            'sort_order' => 1,
        ]);

        Menu::create([
            'name' => 'Password',
            'route' => 'settings.password',
            'icon' => null,
            'parent_id' => $settings->id,
            'sort_order' => 2,
        ]);

        Menu::create([
            'name' => 'Appearance',
            'route' => 'settings.appearance',
            'icon' => null,
            'parent_id' => $settings->id,
            'sort_order' => 3,
        ]);
    }
}
