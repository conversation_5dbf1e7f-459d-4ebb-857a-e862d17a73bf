<?php

namespace App\Helpers;

class Toast
{
    /**
     * Flash a toast message to the session.
     */
    public static function message(string $message): void
    {
        session()->flash('message', $message);
    }

    /**
     * Flash a success toast message to the session.
     */
    public static function success(string $message): void
    {
        session()->flash('success', $message);
    }

    /**
     * Flash an error toast message to the session.
     */
    public static function error(string $message): void
    {
        session()->flash('error', $message);
    }

    /**
     * Flash an info toast message to the session.
     */
    public static function info(string $message): void
    {
        session()->flash('info', $message);
    }
}
