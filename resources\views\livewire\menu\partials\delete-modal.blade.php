<!-- Delete Confirmation Modal -->
<flux:modal wire:model="showDeleteModal" class="min-w-[25rem]">
    @if($deletingMenu)
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Delete Menu: {{ $deletingMenu->name }}</flux:heading>
                <flux:text class="mt-2">
                    <p>You're about to delete the menu named <strong>{{ $deletingMenu->name }}</strong>.</p>
                    <p>This action cannot be reversed.</p>
                </flux:text>
            </div>
            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeDeleteModal" variant="ghost" class="cursor-pointer">Cancel</flux:button>
                <flux:button wire:click="delete" variant="danger" class="cursor-pointer">Delete Menu</flux:button>
            </div>
        </div>
    @endif
</flux:modal>