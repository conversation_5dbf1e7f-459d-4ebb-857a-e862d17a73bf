<!-- Global Toast Component (Shadcn/Sonner Style) -->
<div
    x-data="{
        toasts: [],
        nextId: 1,
        
        addToast(message, options = {}) {
            const toast = {
                id: this.nextId++,
                message: message,
                description: options.description || null,
                action: options.action || null,
                duration: options.duration || 4000,
                dismissible: options.dismissible !== false,
                createdAt: Date.now()
            };
            
            this.toasts.push(toast);
            
            // Auto remove after duration
            if (toast.duration > 0) {
                setTimeout(() => {
                    this.removeToast(toast.id);
                }, toast.duration);
            }
        },
        
        removeToast(id) {
            const index = this.toasts.findIndex(toast => toast.id === id);
            if (index > -1) {
                this.toasts.splice(index, 1);
            }
        },
        
        clearAll() {
            this.toasts = [];
        }
    }"
    x-init="
        // Listen for global toast events
        window.addEventListener('toast', (event) => {
            addToast(event.detail.message, event.detail.options || {});
        });

        // Handle Laravel flash messages
        @if (session()->has('message'))
            addToast('{{ session('message') }}');
        @endif
        @if (session()->has('error'))
            addToast('{{ session('error') }}');
        @endif
        @if (session()->has('success'))
            addToast('{{ session('success') }}');
        @endif
    "
    @toast.window="addToast($event.detail.message, $event.detail.options || {})"
    class="fixed top-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"
>
    <!-- Toast Container -->
    <template x-for="toast in toasts" :key="toast.id">
        <div
            x-show="true"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95 translate-y-2"
            x-transition:enter-end="opacity-100 scale-100 translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 scale-100 translate-y-0"
            x-transition:leave-end="opacity-0 scale-95 translate-y-2"
            class="group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border border-zinc-200 bg-white p-6 pr-8 shadow-lg transition-all dark:border-zinc-800 dark:bg-zinc-950"
            style="margin-bottom: 0.5rem;"
        >
            <!-- Toast Content -->
            <div class="grid gap-1">
                <div class="text-sm font-semibold text-zinc-900 dark:text-zinc-50" x-text="toast.message"></div>
                <div 
                    x-show="toast.description" 
                    class="text-sm opacity-90 text-zinc-500 dark:text-zinc-400" 
                    x-text="toast.description"
                ></div>
            </div>
            
            <!-- Action Button (if provided) -->
            <div x-show="toast.action" class="flex items-center space-x-2">
                <button
                    x-show="toast.action"
                    @click="if(toast.action.onClick) toast.action.onClick(); removeToast(toast.id);"
                    class="inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-transparent px-3 text-xs font-medium ring-offset-white transition-colors hover:bg-zinc-100 focus:outline-none focus:ring-2 focus:ring-zinc-950 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:border-zinc-800 dark:ring-offset-zinc-950 dark:hover:bg-zinc-800 dark:focus:ring-zinc-300"
                    x-text="toast.action ? toast.action.label : ''"
                ></button>
            </div>
            
            <!-- Close Button -->
            <button
                x-show="toast.dismissible"
                @click="removeToast(toast.id)"
                class="absolute right-2 top-2 rounded-md p-1 text-zinc-950/50 opacity-0 transition-opacity hover:text-zinc-950 focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 dark:text-zinc-50/50 dark:hover:text-zinc-50"
            >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <span class="sr-only">Close</span>
            </button>
        </div>
    </template>
</div>

<script>
// Global toast helper function
window.toast = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('toast', {
        detail: { message, options }
    }));
};

// Convenience methods
window.toast.success = function(message, options = {}) {
    window.toast(message, options);
};

window.toast.error = function(message, options = {}) {
    window.toast(message, options);
};

window.toast.info = function(message, options = {}) {
    window.toast(message, options);
};

// Toast with action button
window.toast.action = function(message, actionLabel, actionCallback, options = {}) {
    window.toast(message, {
        ...options,
        action: {
            label: actionLabel,
            onClick: actionCallback
        }
    });
};
</script>
