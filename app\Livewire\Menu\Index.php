<?php

namespace App\Livewire\Menu;

use App\Livewire\Forms\MenuForm;
use App\Models\Menu;
use Livewire\Component;

class Index extends Component
{
    public MenuForm $menuForm;

    public $search = '';
    public $showModal = false;
    public $editingMenu = null;
    public $showDeleteModal = false;
    public $deletingMenu = null;

    public function mount()
    {
        $this->menuForm->reset();
    }

    public function render()
    {
        $menus = $this->getMenus();
        $parentOptions = Menu::roots()->get();

        return view('livewire.menu.index', [
            'menus' => $menus,
            'parentOptions' => $parentOptions,
        ])->layout('components.layouts.app', ['title' => 'Menu Management']);
    }

    private function getMenus()
    {
        $query = Menu::with(['children' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        if ($this->search) {
            return $query->search($this->search)->get();
        }

        return $query->roots()->get();
    }

    public function openCreateModal()
    {
        $this->menuForm->reset();
        $this->editingMenu = null;
        $this->showModal = true;
    }

    public function openEditModal($menuId)
    {
        $menu = Menu::findOrFail($menuId);
        $this->editingMenu = $menu;
        $this->menuForm->setMenu($menu);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->menuForm->reset();
        $this->editingMenu = null;
    }

    public function save()
    {
        try {
            if ($this->editingMenu) {
                // Update existing menu
                $this->menuForm->update($this->editingMenu);
                $this->dispatch('menu-updated');
                session()->flash('message', 'Menu updated successfully!');
            } else {
                // Create new menu
                $this->menuForm->store();
                $this->dispatch('menu-created');
                session()->flash('message', 'Menu created successfully!');
            }

            $this->closeModal();
        } catch (\Exception $exception) {
            session()->flash('error', 'An error occurred while saving the menu: ' . $exception->getMessage());
        }
    }

    public function openDeleteModal($menuId)
    {
        $this->deletingMenu = Menu::find($menuId);
        if ($this->deletingMenu) {
            $this->showDeleteModal = true;
        }
    }

    public function closeDeleteModal()
    {
        $this->showDeleteModal = false;
        $this->deletingMenu = null;
    }

    public function delete()
    {
        if (!$this->deletingMenu) {
            return;
        }

        try {
            $menu = Menu::findOrFail($this->deletingMenu->id);

            // Check if menu has children
            if ($menu->children()->count() > 0) {
                session()->flash('error', 'Cannot delete menu with submenus. Please delete submenus first.');
                $this->closeDeleteModal();
                return;
            }

            $menu->delete();
            $this->dispatch('menu-deleted');
            session()->flash('message', 'Menu deleted successfully!');
        } catch (\Exception $exception) {
            session()->flash('error', 'An error occurred while deleting the menu: ' . $exception->getMessage());
        }

        $this->closeDeleteModal();
    }

    public function reorderMenus($orderedIds)
    {
        try {
            if (empty($orderedIds) || !is_array($orderedIds)) {
                session()->flash('error', 'Invalid menu order data.');
                return;
            }

            Menu::updateSortOrders($orderedIds);
            $this->dispatch('menu-reordered');
            session()->flash('message', 'Menu order updated successfully!');
        } catch (\Exception $exception) {
            session()->flash('error', 'An error occurred while reordering menus: ' . $exception->getMessage());
        }
    }

}
