<?php

namespace Tests\Feature\Livewire\Menu;

use App\Livewire\Menu\Index;
use App\Models\Menu;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class IndexTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_the_menu_index_component()
    {
        Livewire::test(Index::class)
            ->assertStatus(200)
            ->assertSee('Menu Management');
    }

    /** @test */
    public function it_can_open_create_modal()
    {
        Livewire::test(Index::class)
            ->call('openCreateModal')
            ->assertSet('showModal', true)
            ->assertSet('editingMenu', null)
            ->assertSet('menuForm.name', '')
            ->assertSet('menuForm.route', '')
            ->assertSet('menuForm.icon', '')
            ->assertSet('menuForm.parent_id', null);
    }

    /** @test */
    public function it_can_create_a_new_menu()
    {
        Livewire::test(Index::class)
            ->call('openCreateModal')
            ->set('menuForm.name', 'Test Menu')
            ->set('menuForm.route', 'test.menu')
            ->set('menuForm.icon', 'test-icon')
            ->call('save')
            ->assertSet('showModal', false)
            ->assertSessionHas('message', 'Menu created successfully!');

        $this->assertDatabaseHas('menus', [
            'name' => 'Test Menu',
            'route' => 'test.menu',
            'icon' => 'test-icon',
            'parent_id' => null,
        ]);
    }

    /** @test */
    public function it_can_edit_an_existing_menu()
    {
        $menu = Menu::create([
            'name' => 'Original Menu',
            'route' => 'original.menu',
            'icon' => 'original-icon',
            'sort_order' => 1,
        ]);

        Livewire::test(Index::class)
            ->call('openEditModal', $menu->id)
            ->assertSet('showModal', true)
            ->assertSet('editingMenu.id', $menu->id)
            ->assertSet('menuForm.name', 'Original Menu')
            ->assertSet('menuForm.route', 'original.menu')
            ->assertSet('menuForm.icon', 'original-icon')
            ->set('menuForm.name', 'Updated Menu')
            ->call('save')
            ->assertSet('showModal', false)
            ->assertSessionHas('message', 'Menu updated successfully!');

        $this->assertDatabaseHas('menus', [
            'id' => $menu->id,
            'name' => 'Updated Menu',
            'route' => 'original.menu',
            'icon' => 'original-icon',
        ]);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        Livewire::test(Index::class)
            ->call('openCreateModal')
            ->set('menuForm.name', '')
            ->set('menuForm.route', '')
            ->call('save')
            ->assertHasErrors([
                'menuForm.name' => 'required',
                'menuForm.route' => 'required',
            ]);
    }

    /** @test */
    public function it_can_search_menus()
    {
        Menu::create(['name' => 'Dashboard', 'route' => 'dashboard', 'sort_order' => 1]);
        Menu::create(['name' => 'Users', 'route' => 'users.index', 'sort_order' => 2]);

        Livewire::test(Index::class)
            ->set('search', 'Dashboard')
            ->assertSee('Dashboard')
            ->assertDontSee('Users');
    }
}
