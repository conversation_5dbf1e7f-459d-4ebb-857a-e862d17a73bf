<div>
    <!-- Header Section -->
    <div class="mb-6">
        <flux:heading size="xl">Menu Management</flux:heading>
        <flux:subheading>
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="#">Home</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="#">Blog</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>Post</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </flux:subheading>
    </div>



    <!-- Search Section -->
    <div class="mb-6 flex items-center justify-between gap-4">
        <div class="flex-1 max-w-md">
            <flux:input
                wire:model.live="search"
                placeholder="Search menus..."
                icon="magnifying-glass"
            />
        </div>

        <div class="flex-shrink-0">
            <flux:button wire:click="openCreateModal" icon="plus" variant="primary" class="cursor-pointer">
                Create Menu
            </flux:button>
        </div>
    </div>

    @include('livewire.menu.partials.menu-list')

    @include('livewire.menu.partials.modal')

    @include('livewire.menu.partials.delete-modal')

    @include('livewire.menu.partials.scripts')
</div>
