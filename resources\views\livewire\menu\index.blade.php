<div>
    <!-- Header Section -->
    <div class="mb-6">
        <flux:heading size="xl">Menu Management</flux:heading>
        <flux:subheading>
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="#">Home</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="#">Blog</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>Post</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </flux:subheading>
    </div>



    <!-- Search Section -->
    <div class="mb-6 flex items-center justify-between gap-4">
        <div class="flex-1 max-w-md">
            <flux:input
                wire:model.live="search"
                placeholder="Search menus..."
                icon="magnifying-glass"
            />
        </div>

        <div class="flex-shrink-0 space-x-2">
            <!-- Test Animation Buttons (temporary) -->
            <flux:button
                onclick="toast('🎉 Single toast!', { description: 'Check out the smooth animations!' })"
                variant="ghost"
                size="sm"
                class="cursor-pointer"
            >
                Test Single
            </flux:button>

            <flux:button
                onclick="
                    toast('📝 First toast', { description: 'This is the first toast' });
                    toast('✅ Second toast', { description: 'This is the second toast' });
                    toast('🚀 Third toast', { description: 'This is the third toast' });
                "
                variant="ghost"
                size="sm"
                class="cursor-pointer"
            >
                Test Multiple
            </flux:button>

            <flux:button wire:click="openCreateModal" icon="plus" variant="primary" class="cursor-pointer">
                Create Menu
            </flux:button>
        </div>
    </div>

    @include('livewire.menu.partials.menu-list')

    @include('livewire.menu.partials.modal')

    @include('livewire.menu.partials.delete-modal')

    @include('livewire.menu.partials.scripts')
</div>
