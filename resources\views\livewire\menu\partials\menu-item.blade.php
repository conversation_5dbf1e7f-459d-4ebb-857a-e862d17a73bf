<div class="menu-item bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4"
     data-menu-id="{{ $menu->id }}">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3 flex-1">
            <!-- Drag Handle -->
            <div class="drag-handle cursor-grab active:cursor-grabbing text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300 p-1 rounded hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-colors" title="Drag to reorder">
                <flux:icon name="bars-3" class="w-5 h-5" />
            </div>

            <!-- Accordion Toggle (if has children) -->
            @if($menu->children->count() > 0)
                <button
                    class="accordion-toggle p-1 rounded hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-colors cursor-pointer"
                    onclick="toggleAccordion('menu-{{ $menu->id }}')"
                    title="Toggle submenu"
                >
                    <flux:icon name="chevron-right" class="w-4 h-4 text-zinc-500 transition-transform duration-200" id="chevron-{{ $menu->id }}" />
                </button>
            @endif

            <!-- Menu Icon -->
            @if($menu->icon)
                <flux:icon name="{{ $menu->icon }}" class="w-5 h-5 text-zinc-500" />
            @endif

            <!-- Menu Details -->
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <h3 class="font-medium text-zinc-900 dark:text-zinc-100">{{ $menu->name }}</h3>
                    @if($menu->children->count() > 0)
                        <span class="text-xs bg-zinc-200 dark:bg-zinc-600 text-zinc-600 dark:text-zinc-300 px-2 py-1 rounded-full">
                            {{ $menu->children->count() }} items
                        </span>
                    @endif
                </div>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">{{ $menu->route }}</p>
                @if($menu->parent)
                    <p class="text-xs text-zinc-400 dark:text-zinc-500">Parent: {{ $menu->parent->name }}</p>
                @endif
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center space-x-2">
            <flux:button
                wire:click="openEditModal({{ $menu->id }})"
                size="sm"
                variant="ghost"
                icon="pencil"
                class="cursor-pointer"
            >
                Edit
            </flux:button>

            <flux:button
                wire:click="openDeleteModal({{ $menu->id }})"
                size="sm"
                variant="danger"
                icon="trash"
                class="cursor-pointer"
            >
                Delete
            </flux:button>
        </div>
    </div>

    <!-- Child Menus (Accordion) -->
    @if($menu->children->count() > 0)
        <div
            id="submenu-menu-{{ $menu->id }}"
            class="submenu-container mt-4 ml-8 space-y-2 hidden overflow-hidden transition-all duration-300"
        >
            <div id="submenu-list-{{ $menu->id }}" class="submenu-list space-y-2">
                @foreach($menu->children as $child)
                    <div class="submenu-item bg-zinc-50 dark:bg-zinc-700 rounded-md border border-zinc-100 dark:border-zinc-600 p-3"
                         data-menu-id="{{ $child->id }}"
                         data-parent-id="{{ $menu->id }}">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="submenu-drag-handle cursor-grab active:cursor-grabbing text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300 p-1 rounded hover:bg-zinc-200 dark:hover:bg-zinc-600 transition-colors" title="Drag to reorder">
                                    <flux:icon name="bars-3" class="w-4 h-4" />
                                </div>

                                @if($child->icon)
                                    <flux:icon name="{{ $child->icon }}" class="w-4 h-4 text-zinc-500" />
                                @endif

                                <div>
                                    <h4 class="font-medium text-zinc-800 dark:text-zinc-200">{{ $child->name }}</h4>
                                    <p class="text-sm text-zinc-500 dark:text-zinc-400">{{ $child->route }}</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-2">
                                <flux:button
                                    wire:click="openEditModal({{ $child->id }})"
                                    size="sm"
                                    variant="ghost"
                                    icon="pencil"
                                >
                                    Edit
                                </flux:button>

                                <flux:button
                                    wire:click="openDeleteModal({{ $child->id }})"
                                    size="sm"
                                    variant="danger"
                                    icon="trash"
                                    class="cursor-pointer"
                                >
                                    Delete
                                </flux:button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>