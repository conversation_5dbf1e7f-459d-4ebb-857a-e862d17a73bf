<!-- Create/Edit Modal -->
<flux:modal wire:model="showModal" class="max-w-2xl min-w-[40rem]">
    <form wire:submit="save">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">
                    {{ $editingMenu ? 'Edit Menu' : 'Create Menu' }}
                </flux:heading>
                <flux:subheading>
                    {{ $editingMenu ? 'Update menu details' : 'Add a new menu item' }}
                </flux:subheading>
            </div>

            <div class="space-y-4">
                <!-- Name Field -->
                <flux:field>
                    <flux:label>Menu Name *</flux:label>
                    <flux:input
                        wire:model.live.debounce.300ms="menuForm.name"
                        placeholder="Enter menu name (e.g., Dashboard, Users)"
                        required
                        maxlength="255"
                    />
                    <flux:error name="menuForm.name" />
                </flux:field>

                <!-- Route Field -->
                <flux:field>
                    <flux:label>Route Name *</flux:label>
                    <flux:input
                        wire:model.live.debounce.300ms="menuForm.route"
                        placeholder="Enter route name (e.g., dashboard, users.index, documents.create)"
                        required
                        maxlength="255"
                    />
                    <flux:error name="menuForm.route" />
                </flux:field>

                <!-- Icon Field -->
                <flux:field>
                    <flux:label>
                        Icon (Optional)
                        <flux:tooltip toggleable>
                            <flux:button icon="information-circle" size="xs" variant="ghost" />
                            <flux:tooltip.content class="max-w-[20rem] space-y-2">
                                <p>Icon name from available Flux icons. Leave empty for no icon. Use letters, numbers, hyphens, and underscores only.</p>
                            </flux:tooltip.content>
                        </flux:tooltip>
                    </flux:label>
                    <flux:input
                        wire:model.live.debounce.300ms="menuForm.icon"
                        placeholder="Enter icon name (e.g., layout-grid, bars-3, folder-git-2)"
                        maxlength="50"
                    />
                    <flux:error name="menuForm.icon" />
                </flux:field>

                <!-- Parent Menu Field -->
                <flux:field>
                    <flux:label>
                        Parent Menu (Optional)
                        <flux:tooltip toggleable>
                            <flux:button icon="information-circle" size="xs" variant="ghost" />
                            <flux:tooltip.content class="max-w-[20rem] space-y-2">
                                <p>Select a parent menu to create this as a submenu. Leave empty to create a root-level menu.</p>
                            </flux:tooltip.content>
                        </flux:tooltip>
                    </flux:label>
                    <flux:select wire:model="menuForm.parent_id" placeholder="Select parent menu to create a submenu">
                        <flux:select.option value="">No Parent (Root Menu)</flux:select.option>
                        @foreach($parentOptions as $parent)
                            @if(!$editingMenu || $parent->id !== $editingMenu->id)
                                <flux:select.option value="{{ $parent->id }}">{{ $parent->name }}</flux:select.option>
                            @endif
                        @endforeach
                    </flux:select>
                    <flux:error name="menuForm.parent_id" />
                </flux:field>

                <!-- Preview Section -->
                @if($menuForm->name || $menuForm->route || $menuForm->icon)
                    <div class="p-4 bg-zinc-50 dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700">
                        <flux:label class="mb-2">Preview</flux:label>
                        <div class="flex items-center space-x-3">
                            @if($menuForm->icon)
                                <div class="w-5 h-5 bg-zinc-300 dark:bg-zinc-600 rounded flex items-center justify-center">
                                    <span class="text-xs text-zinc-600 dark:text-zinc-400">{{ substr($menuForm->icon, 0, 2) }}</span>
                                </div>
                            @endif
                            <div>
                                <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                    {{ $menuForm->name ?: 'Menu Name' }}
                                </div>
                                <div class="text-sm text-zinc-500 dark:text-zinc-400">
                                    {{ $menuForm->route ?: 'route.name' }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="flex justify-end space-x-2">
                <flux:button type="button" wire:click="closeModal" variant="ghost" class="cursor-pointer">
                    Cancel
                </flux:button>
                <flux:button type="submit" variant="primary" class="cursor-pointer">
                    {{ $editingMenu ? 'Update Menu' : 'Create Menu' }}
                </flux:button>
            </div>
        </div>
    </form>
</flux:modal>