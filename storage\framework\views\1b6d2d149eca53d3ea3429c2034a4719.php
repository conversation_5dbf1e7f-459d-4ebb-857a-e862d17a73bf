<!--
Global Toast Component (Shadcn/Sonner Style)
Styled to match your Flux UI app.css design system

Usage:
- Livewire: $this->dispatch('toast', message: 'Hello world!')
- JavaScript: toast('Hello world!')
- With description: toast('Title', { description: 'Description text' })
- With action: toast.action('File deleted', 'Undo', () => { /* undo logic */ })
-->
<div
    x-data="{
        toasts: [],
        nextId: 1,
        
        addToast(message, options = {}) {
            const toast = {
                id: this.nextId++,
                message: message,
                description: options.description || null,
                action: options.action || null,
                duration: options.duration || 4000,
                dismissible: options.dismissible !== false,
                createdAt: Date.now(),
                progress: 100
            };

            this.toasts.push(toast);

            // Auto remove after duration with progress animation
            if (toast.duration > 0) {
                // Animate progress bar
                const interval = setInterval(() => {
                    const elapsed = Date.now() - toast.createdAt;
                    const remaining = Math.max(0, toast.duration - elapsed);
                    toast.progress = (remaining / toast.duration) * 100;

                    if (remaining <= 0) {
                        clearInterval(interval);
                        this.removeToast(toast.id);
                    }
                }, 50);
            }
        },
        
        removeToast(id) {
            const index = this.toasts.findIndex(toast => toast.id === id);
            if (index > -1) {
                this.toasts.splice(index, 1);
            }
        },
        
        clearAll() {
            this.toasts = [];
        }
    }"
    x-init="
        // Listen for global toast events
        window.addEventListener('toast', (event) => {
            addToast(event.detail.message, event.detail.options || {});
        });

        // Handle Laravel flash messages
        <?php if(session()->has('message')): ?>
            addToast('<?php echo e(session('message')); ?>');
        <?php endif; ?>
        <?php if(session()->has('error')): ?>
            addToast('<?php echo e(session('error')); ?>');
        <?php endif; ?>
        <?php if(session()->has('success')): ?>
            addToast('<?php echo e(session('success')); ?>');
        <?php endif; ?>
    "
    @toast.window="addToast($event.detail.message, $event.detail.options || {})"
    class="fixed bottom-0 right-0 z-[100] flex max-h-screen w-full flex-col p-4 sm:max-w-[420px]"
>
    <!-- Toast Container -->
    <template x-for="toast in toasts" :key="toast.id">
        <div
            x-show="true"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95 translate-y-2"
            x-transition:enter-end="opacity-100 scale-100 translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 scale-100 translate-y-0"
            x-transition:leave-end="opacity-0 scale-95 translate-y-2"
            class="group pointer-events-auto relative flex w-full items-start space-x-3 overflow-hidden rounded-lg border border-zinc-200 bg-white p-4 shadow-lg transition-all hover:shadow-xl dark:border-zinc-700 dark:bg-zinc-900 border-l-4 border-l-accent"
            style="margin-bottom: 0.75rem;"
            role="alert"
            aria-live="polite"
        >
            <!-- Toast Icon -->
            <div class="flex-shrink-0 mt-0.5">
                <div class="flex h-5 w-5 items-center justify-center rounded-full bg-accent/10">
                    <svg class="h-3 w-3 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Toast Content -->
            <div class="flex-1 grid gap-1">
                <div class="text-sm font-medium text-zinc-900 dark:text-zinc-100" x-text="toast.message"></div>
                <div
                    x-show="toast.description"
                    class="text-sm text-zinc-600 dark:text-zinc-400"
                    x-text="toast.description"
                ></div>
            </div>
            
            <!-- Action Button (if provided) -->
            <div x-show="toast.action" class="flex items-center space-x-2">
                <button
                    x-show="toast.action"
                    @click="if(toast.action.onClick) toast.action.onClick(); removeToast(toast.id);"
                    class="inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-transparent px-3 text-xs font-medium transition-colors hover:bg-zinc-50 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:border-zinc-700 dark:hover:bg-zinc-800 dark:focus:ring-accent"
                    x-text="toast.action ? toast.action.label : ''"
                ></button>
            </div>
            
            <!-- Close Button -->
            <button
                x-show="toast.dismissible"
                @click="removeToast(toast.id)"
                class="flex-shrink-0 rounded-md p-1.5 text-zinc-400 opacity-0 transition-all hover:text-zinc-600 hover:bg-zinc-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-1 group-hover:opacity-100 dark:text-zinc-500 dark:hover:text-zinc-300 dark:hover:bg-zinc-800"
            >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <span class="sr-only">Close</span>
            </button>

            <!-- Progress Bar -->
            <div
                x-show="toast.duration > 0"
                class="absolute bottom-0 left-0 h-1 bg-accent transition-all duration-75 ease-linear"
                :style="`width: ${toast.progress}%`"
            ></div>
        </div>
    </template>
</div>

<script>
// Global toast helper function
window.toast = function(message, options = {}) {
    window.dispatchEvent(new CustomEvent('toast', {
        detail: { message, options }
    }));
};

// Convenience methods
window.toast.success = function(message, options = {}) {
    window.toast(message, options);
};

window.toast.error = function(message, options = {}) {
    window.toast(message, options);
};

window.toast.info = function(message, options = {}) {
    window.toast(message, options);
};

// Toast with action button
window.toast.action = function(message, actionLabel, actionCallback, options = {}) {
    window.toast(message, {
        ...options,
        action: {
            label: actionLabel,
            onClick: actionCallback
        }
    });
};
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\Sodiq\document-management-system\resources\views/components/toast.blade.php ENDPATH**/ ?>