<?php

namespace App\Livewire\Forms;

use App\Models\Menu;
use Livewire\Attributes\Validate;
use Livewire\Form;

class MenuForm extends Form
{
    #[Validate([
        'required',
        'string',
        'max:255',
        'min:2',
        'regex:/^[a-zA-Z0-9\s\-_]+$/'
    ], message: [
        'required' => 'Menu name is required.',
        'min' => 'Menu name must be at least 2 characters.',
        'max' => 'Menu name cannot exceed 255 characters.',
        'regex' => 'Menu name can only contain letters, numbers, spaces, hyphens, and underscores.',
    ])]
    public $name = '';

    #[Validate([
        'required',
        'string',
        'max:255',
        'min:2',
        'regex:/^[a-zA-Z0-9\.\-_]+$/'
    ], message: [
        'required' => 'Route is required.',
        'min' => 'Route must be at least 2 characters.',
        'max' => 'Route cannot exceed 255 characters.',
        'regex' => 'Route can only contain letters, numbers, dots, hyphens, and underscores.',
    ])]
    public $route = '';

    #[Validate([
        'nullable',
        'string',
        'max:50',
        'regex:/^[a-zA-Z0-9\-_]+$/'
    ], message: [
        'max' => 'Icon name cannot exceed 50 characters.',
        'regex' => 'Icon name can only contain letters, numbers, hyphens, and underscores.',
    ])]
    public $icon = '';

    #[Validate([
        'nullable',
        'exists:menus,id'
    ], message: [
        'exists' => 'Selected parent menu does not exist.',
    ])]
    public $parent_id = null;

    /**
     * Set the form data from a menu model.
     */
    public function setMenu(Menu $menu): void
    {
        $this->name = $menu->name;
        $this->route = $menu->route;
        $this->icon = $menu->icon;
        $this->parent_id = $menu->parent_id;
    }

    /**
     * Reset the form to empty values.
     */
    public function reset(...$properties): void
    {
        $this->name = '';
        $this->route = '';
        $this->icon = '';
        $this->parent_id = null;
    }

    /**
     * Create a new menu.
     */
    public function store(): Menu
    {
        $this->validate();

        $data = $this->only(['name', 'route', 'icon', 'parent_id']);
        $data['sort_order'] = Menu::getNextSortOrder($this->parent_id);

        return Menu::create($data);
    }

    /**
     * Update an existing menu.
     */
    public function update(Menu $menu): bool
    {
        $this->validate();

        return $menu->update($this->only(['name', 'route', 'icon', 'parent_id']));
    }
}
